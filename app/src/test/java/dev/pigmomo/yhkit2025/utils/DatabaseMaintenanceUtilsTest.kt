package dev.pigmomo.yhkit2025.utils

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.LogEntity
import dev.pigmomo.yhkit2025.data.repository.LogRepositoryImpl
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.util.Date

/**
 * 数据库维护工具测试类
 */
@RunWith(AndroidJUnit4::class)
class DatabaseMaintenanceUtilsTest {
    
    private lateinit var database: AppDatabase
    private lateinit var context: Context
    private lateinit var logRepository: LogRepositoryImpl
    
    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        database = Room.inMemoryDatabaseBuilder(
            context,
            AppDatabase::class.java
        ).allowMainThreadQueries().build()
        
        logRepository = LogRepositoryImpl(database.logDao())
    }
    
    @After
    fun tearDown() {
        database.close()
    }
    
    @Test
    fun testDatabaseHealthCheck_EmptyDatabase() = runBlocking {
        val healthInfo = DatabaseMaintenanceUtils.checkDatabaseHealth(context)
        
        assertTrue("Empty database should be healthy", healthInfo.isHealthy)
        assertEquals("Empty database should have 0 logs", 0, healthInfo.totalLogCount)
        assertTrue("Query time should be reasonable", healthInfo.queryResponseTimeMs < 1000)
    }
    
    @Test
    fun testDatabaseHealthCheck_WithLogs() = runBlocking {
        // 添加一些测试日志
        val testLogs = (1..100).map { i ->
            LogEntity(
                tokenUid = "token_$i",
                message = "Test message $i",
                timestamp = Date(System.currentTimeMillis() - i * 1000),
                tag = "TEST",
                logLevel = "INFO",
                phoneNumber = "**********"
            )
        }
        
        logRepository.saveLogs(testLogs)
        
        val healthInfo = DatabaseMaintenanceUtils.checkDatabaseHealth(context)
        
        assertTrue("Database with logs should be healthy", healthInfo.isHealthy)
        assertEquals("Should have 100 logs", 100, healthInfo.totalLogCount)
    }
    
    @Test
    fun testDatabaseCleanup() = runBlocking {
        // 添加一些旧日志和新日志
        val oldLogs = (1..50).map { i ->
            LogEntity(
                tokenUid = "old_token_$i",
                message = "Old message $i",
                timestamp = Date(System.currentTimeMillis() - 10 * 24 * 60 * 60 * 1000L), // 10天前
                tag = "TEST",
                logLevel = "INFO",
                phoneNumber = "**********"
            )
        }
        
        val newLogs = (1..30).map { i ->
            LogEntity(
                tokenUid = "new_token_$i",
                message = "New message $i",
                timestamp = Date(System.currentTimeMillis() - i * 1000), // 最近的日志
                tag = "TEST",
                logLevel = "INFO",
                phoneNumber = "**********"
            )
        }
        
        logRepository.saveLogs(oldLogs + newLogs)
        
        // 执行清理（保留7天内的日志）
        val cleanupResult = DatabaseMaintenanceUtils.performDatabaseCleanup(context, 7)
        
        assertTrue("Cleanup should succeed", cleanupResult.success)
        assertEquals("Should delete 50 old logs", 50, cleanupResult.deletedLogCount)
        
        // 验证剩余日志数量
        val remainingCount = logRepository.getLogCount()
        assertEquals("Should have 30 logs remaining", 30, remainingCount)
    }
    
    @Test
    fun testProgressManagerSyncWithTimeout() = runBlocking {
        // 创建一些测试令牌
        val orderTokens = (1..10).map { i ->
            dev.pigmomo.yhkit2025.data.model.OrderTokenEntity(
                uid = "token_$i",
                phoneNumber = "**********",
                orderIndex = i
            )
        }
        
        // 为每个令牌添加日志
        orderTokens.forEach { token ->
            logRepository.saveLog(
                tokenUid = token.uid,
                message = "Progress for ${token.uid}",
                tag = "TEST",
                logLevel = "INFO",
                phoneNumber = token.phoneNumber
            )
        }
        
        // 测试同步功能
        ProgressManager.syncLatestRecordFromDbByOrderTokens(
            context = context,
            orderTokens = orderTokens,
            batchSize = 5,
            timeoutPerBatch = 3000,
            singleQueryTimeout = 500
        )
        
        // 等待同步完成
        kotlinx.coroutines.delay(1000)
        
        // 验证进度记录是否正确同步
        val progressRecords = ProgressManager.progressRecordsFlow.value
        assertEquals("Should sync all tokens", orderTokens.size, progressRecords.size)
        
        orderTokens.forEach { token ->
            assertTrue("Should contain progress for ${token.uid}", 
                progressRecords.containsKey(token.uid))
            assertEquals("Progress message should match", 
                "Progress for ${token.uid}", 
                progressRecords[token.uid])
        }
    }
    
    @Test
    fun testDatabaseExceptionHandling() = runBlocking {
        // 测试数据库异常处理
        val result = logRepository.getLatestLogByToken("non_existent_token")
        assertNull("Should return null for non-existent token", result)
        
        // 测试超时处理
        val startTime = System.currentTimeMillis()
        val timeoutResult = kotlinx.coroutines.withTimeoutOrNull(100) {
            // 模拟一个可能很慢的查询
            logRepository.getLatestLogByToken("test_token")
        }
        val endTime = System.currentTimeMillis()
        
        assertTrue("Should complete within timeout", endTime - startTime < 200)
    }
}
